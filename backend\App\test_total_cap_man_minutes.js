const massive = require('massive');
require('dotenv').config();

async function testTotalCapManMinutes() {
    try {
        // Connect to database
        const db = await massive(process.env.DB_URL);
        console.log('Connected to database');

        // Test the helper function
        console.log('\n=== Testing tms_hlpr_calculate_total_cap_man_minutes ===');
        
        const testUserId = '550e8400-e29b-41d4-a716-************'; // Sample UUID
        const testOrgId = 1;
        const slotStartTime = '2024-12-04 13:00:00+00'; // 1 PM UTC
        const slotEndTime = '2024-12-04 14:00:00+00';   // 2 PM UTC

        console.log(`Test User ID: ${testUserId}`);
        console.log(`Slot Time: ${slotStartTime} to ${slotEndTime}`);
        console.log('');

        // Clean up any existing test data
        await db.query(`
            DELETE FROM public.cl_tx_usr_availability 
            WHERE user_id = $1 AND org_id = $2
        `, [testUserId, testOrgId]);

        // Test Case 1: No availability records
        let result = await db.query(`
            SELECT tms_hlpr_calculate_total_cap_man_minutes($1, $2, $3, $4) as result
        `, [testUserId, testOrgId, slotStartTime, slotEndTime]);
        console.log(`Test Case 1 - No availability records: ${result[0].result} minutes`);

        // Test Case 2: Full availability (1 PM - 2 PM, 60 minutes)
        await db.query(`
            INSERT INTO public.cl_tx_usr_availability (
                user_id, org_id, usr_tmzone_day, start_time, end_time,
                is_present, is_active
            ) VALUES ($1, $2, $3, $4, $5, true, true)
        `, [testUserId, testOrgId, '2024-12-04', '2024-12-04 13:00:00', '2024-12-04 14:00:00']);

        result = await db.query(`
            SELECT tms_hlpr_calculate_total_cap_man_minutes($1, $2, $3, $4) as result
        `, [testUserId, testOrgId, slotStartTime, slotEndTime]);
        console.log(`Test Case 2 - Full availability (1 PM - 2 PM): ${result[0].result} minutes`);

        // Clear data for next test
        await db.query(`
            DELETE FROM public.cl_tx_usr_availability
            WHERE user_id = $1 AND org_id = $2
        `, [testUserId, testOrgId]);

        // Test Case 3: Partial availability (1:30 PM - 2 PM, 30 minutes overlap)
        await db.query(`
            INSERT INTO public.cl_tx_usr_availability (
                user_id, org_id, usr_tmzone_day, start_time, end_time,
                is_present, is_active
            ) VALUES ($1, $2, $3, $4, $5, true, true)
        `, [testUserId, testOrgId, '2024-12-04', '2024-12-04 13:30:00', '2024-12-04 14:00:00']);

        result = await db.query(`
            SELECT tms_hlpr_calculate_total_cap_man_minutes($1, $2, $3, $4) as result
        `, [testUserId, testOrgId, slotStartTime, slotEndTime]);
        console.log(`Test Case 3 - Partial availability (1:30 PM - 2 PM): ${result[0].result} minutes`);

        // Clear data for next test
        await db.query(`
            DELETE FROM public.cl_tx_usr_availability
            WHERE user_id = $1 AND org_id = $2
        `, [testUserId, testOrgId]);

        // Test Case 4: Multiple availability slots with gaps
        // Slot 1: 1:00 PM - 1:15 PM (15 minutes)
        await db.query(`
            INSERT INTO public.cl_tx_usr_availability (
                user_id, org_id, usr_tmzone_day, start_time, end_time,
                is_present, is_active
            ) VALUES ($1, $2, $3, $4, $5, true, true)
        `, [testUserId, testOrgId, '2024-12-04', '2024-12-04 13:00:00', '2024-12-04 13:15:00']);
        
        // Slot 2: 1:30 PM - 1:45 PM (15 minutes)
        await db.query(`
            INSERT INTO public.cl_tx_usr_availability (
                user_id, org_id, usr_tmzone_day, start_time, end_time,
                is_present, is_active
            ) VALUES ($1, $2, $3, $4, $5, true, true)
        `, [testUserId, testOrgId, '2024-12-04', '2024-12-04 13:30:00', '2024-12-04 13:45:00']);

        result = await db.query(`
            SELECT tms_hlpr_calculate_total_cap_man_minutes($1, $2, $3, $4) as result
        `, [testUserId, testOrgId, slotStartTime, slotEndTime]);
        console.log(`Test Case 4 - Multiple availability slots (15 + 15 minutes): ${result[0].result} minutes`);

        // Clean up test data
        await db.query(`
            DELETE FROM public.cl_tx_usr_availability
            WHERE user_id = $1 AND org_id = $2
        `, [testUserId, testOrgId]);

        console.log('\n=== Testing tms_ace_get_capacity_data_by_resource ===');
        
        // Test the main function with a sample resource
        try {
            const capacityResult = await db.query(`
                SELECT * FROM tms_ace_get_capacity_data_by_resource($1, $2, $3, $4, $5)
                LIMIT 1
            `, [1, 3, 5, '2', '2024-12-04']);
            
            if (capacityResult.length > 0) {
                console.log('Sample capacity data with total_cap_man_minutes:');
                console.log(JSON.stringify(capacityResult[0], null, 2));
            } else {
                console.log('No capacity data found for the test parameters');
            }
        } catch (error) {
            console.log('Error testing main function:', error.message);
        }

        console.log('\nTest completed successfully!');
        
    } catch (error) {
        console.error('Error:', error);
    } finally {
        process.exit(0);
    }
}

testTotalCapManMinutes();
