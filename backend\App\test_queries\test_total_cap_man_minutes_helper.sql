-- Test script for tms_hlpr_calculate_total_cap_man_minutes function
DO $$
DECLARE
    test_user_id uuid := gen_random_uuid();
    test_org_id integer := 1;
    slot_start_time timestamp with time zone := '2024-12-04 13:00:00+00'::timestamp with time zone; -- 1 PM UTC
    slot_end_time timestamp with time zone := '2024-12-04 14:00:00+00'::timestamp with time zone;   -- 2 PM UTC
    result numeric;
BEGIN
    RAISE NOTICE 'Testing tms_hlpr_calculate_total_cap_man_minutes function';
    RAISE NOTICE '=======================================================';
    RAISE NOTICE 'Test User ID: %', test_user_id;
    RAISE NOTICE 'Slot Time: % to %', slot_start_time, slot_end_time;
    RAISE NOTICE '';

    -- Clean up any existing test data
    DELETE FROM public.cl_tx_usr_availability 
    WHERE user_id = test_user_id AND org_id = test_org_id;

    -- Test Case 1: No availability records
    result := tms_hlpr_calculate_total_cap_man_minutes(test_user_id, test_org_id, slot_start_time, slot_end_time);
    RAISE NOTICE 'Test Case 1 - No availability records: % minutes', result;

    -- Test Case 2: Full availability (1 PM - 2 PM, 60 minutes)
    INSERT INTO public.cl_tx_usr_availability (
        user_id, org_id, usr_tmzone_day, start_time, end_time,
        is_present, is_active
    ) VALUES (
        test_user_id, test_org_id, '2024-12-04'::date,
        '2024-12-04 13:00:00'::timestamp, '2024-12-04 14:00:00'::timestamp,
        true, true
    );

    result := tms_hlpr_calculate_total_cap_man_minutes(test_user_id, test_org_id, slot_start_time, slot_end_time);
    RAISE NOTICE 'Test Case 2 - Full availability (1 PM - 2 PM): % minutes', result;

    -- Clear data for next test
    DELETE FROM public.cl_tx_usr_availability
    WHERE user_id = test_user_id AND org_id = test_org_id;

    -- Test Case 3: Partial availability (1:30 PM - 2 PM, 30 minutes overlap)
    INSERT INTO public.cl_tx_usr_availability (
        user_id, org_id, usr_tmzone_day, start_time, end_time,
        is_present, is_active
    ) VALUES (
        test_user_id, test_org_id, '2024-12-04'::date,
        '2024-12-04 13:30:00'::timestamp, '2024-12-04 14:00:00'::timestamp,
        true, true
    );

    result := tms_hlpr_calculate_total_cap_man_minutes(test_user_id, test_org_id, slot_start_time, slot_end_time);
    RAISE NOTICE 'Test Case 3 - Partial availability (1:30 PM - 2 PM): % minutes', result;

    -- Clear data for next test
    DELETE FROM public.cl_tx_usr_availability
    WHERE user_id = test_user_id AND org_id = test_org_id;

    -- Test Case 4: Availability extends beyond slot (12:30 PM - 2:30 PM, 60 minutes overlap)
    INSERT INTO public.cl_tx_usr_availability (
        user_id, org_id, usr_tmzone_day, start_time, end_time,
        is_present, is_active
    ) VALUES (
        test_user_id, test_org_id, '2024-12-04'::date,
        '2024-12-04 12:30:00'::timestamp, '2024-12-04 14:30:00'::timestamp,
        true, true
    );

    result := tms_hlpr_calculate_total_cap_man_minutes(test_user_id, test_org_id, slot_start_time, slot_end_time);
    RAISE NOTICE 'Test Case 4 - Availability extends beyond slot (12:30 PM - 2:30 PM): % minutes', result;

    -- Clear data for next test
    DELETE FROM public.cl_tx_usr_availability
    WHERE user_id = test_user_id AND org_id = test_org_id;

    -- Test Case 5: Multiple availability slots with gaps
    -- Slot 1: 1:00 PM - 1:15 PM (15 minutes)
    INSERT INTO public.cl_tx_usr_availability (
        user_id, org_id, usr_tmzone_day, start_time, end_time,
        is_present, is_active
    ) VALUES (
        test_user_id, test_org_id, '2024-12-04'::date,
        '2024-12-04 13:00:00'::timestamp, '2024-12-04 13:15:00'::timestamp,
        true, true
    );
    
    -- Slot 2: 1:30 PM - 1:45 PM (15 minutes)
    INSERT INTO public.cl_tx_usr_availability (
        user_id, org_id, usr_tmzone_day, start_time, end_time,
        is_present, is_active
    ) VALUES (
        test_user_id, test_org_id, '2024-12-04'::date,
        '2024-12-04 13:30:00'::timestamp, '2024-12-04 13:45:00'::timestamp,
        true, true
    );

    result := tms_hlpr_calculate_total_cap_man_minutes(test_user_id, test_org_id, slot_start_time, slot_end_time);
    RAISE NOTICE 'Test Case 5 - Multiple availability slots (15 + 15 minutes): % minutes', result;

    -- Clean up test data
    DELETE FROM public.cl_tx_usr_availability
    WHERE user_id = test_user_id AND org_id = test_org_id;

    RAISE NOTICE '';
    RAISE NOTICE 'Test completed successfully!';
END $$;
