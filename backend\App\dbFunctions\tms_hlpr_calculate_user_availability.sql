DROP FUNCTION IF EXISTS public.tms_hlpr_calculate_user_availability(uuid, integer, timestamp with time zone, timestamp with time zone);

CREATE OR REPLACE FUNCTION public.tms_hlpr_calculate_user_availability(
    p_user_id uuid,
    p_org_id integer,
    p_start_time timestamp with time zone,
    p_end_time timestamp with time zone,
    p_convert_time_to_utc boolean default false
)
RETURNS numeric
LANGUAGE plpgsql AS $function$
DECLARE
    total_duration numeric;
    available_duration numeric;
    availability_percentage numeric;
    start_time_utc timestamp;
    end_time_utc timestamp;
BEGIN
    
    if p_convert_time_to_utc then
        -- Convert timezone-aware input to timezone-naive UTC to match stored data
        -- Stored data format: 2025-08-20 10:30:00.000 (without timezone)
        start_time_utc := (p_start_time AT TIME ZONE 'UTC')::timestamp;
        end_time_utc := (p_end_time AT TIME ZONE 'UTC')::timestamp;
    else
    	start_time_utc := p_start_time::timestamp;
    	end_time_utc := p_end_time::timestamp;
    end if;
   
     -- Calculate the total duration of the time window in seconds
    total_duration := EXTRACT(EPOCH FROM (end_time_utc - start_time_utc));
  
    -- If total duration is zero or negative, return 0
    IF total_duration <= 0 THEN
        RETURN 0;
    END IF;
    -- Calculate the total duration of available slots that overlap with the time window
    SELECT COALESCE(
               SUM(
                   CASE
                       -- When slot is fully within our time window
                       WHEN avail.start_time >= start_time_utc AND avail.end_time <= end_time_utc THEN
                           EXTRACT(EPOCH FROM (avail.end_time - avail.start_time))
                       -- When slot starts before our window but ends within it
                       WHEN avail.start_time < start_time_utc AND avail.end_time <= end_time_utc AND avail.end_time > start_time_utc THEN
                           EXTRACT(EPOCH FROM (avail.end_time - start_time_utc))
                       -- When slot starts within our window but ends after it
                       WHEN avail.start_time >= start_time_utc AND avail.start_time < end_time_utc AND avail.end_time > end_time_utc THEN
                           EXTRACT(EPOCH FROM (end_time_utc - avail.start_time))
                       -- When slot completely encompasses our window
                       WHEN avail.start_time < start_time_utc AND avail.end_time > end_time_utc THEN
                           EXTRACT(EPOCH FROM (end_time_utc - start_time_utc))
                       ELSE 0
                   END
               ),
               0
           )
      INTO available_duration
      FROM public.cl_tx_usr_availability avail
     WHERE avail.user_id = p_user_id
       AND avail.org_id = p_org_id
       AND avail.is_active = true
       AND avail.is_present = true
       AND avail.usr_tmzone_day = start_time_utc::date
       -- Only consider slots that overlap with our time window
       AND avail.start_time < end_time_utc
       AND avail.end_time > start_time_utc;
    -- Calculate the availability percentage
    availability_percentage := available_duration / total_duration;

    -- If all micro slots are present and cover the entire time window, return 1
    -- Otherwise, return the calculated percentage
    IF availability_percentage >= 0.999 THEN
        RETURN 1;
    ELSE
        RETURN availability_percentage;
    END IF;
END;
$function$;