import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import BookingDayCards from './BookingDayCards';

beforeEach(() => {
    console.info('Console logs disabled!');
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'group').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
});

describe('BookingDayCards Component Tests', () => {
    const mockWeekData = {
        days: [
            {
                date: '2024-12-04',
                dayName: 'Wednesday',
                displayDate: '4th Dec'
            },
            {
                date: '2024-12-05',
                dayName: 'Thursday',
                displayDate: '5th Dec'
            }
        ]
    };

    const mockHandleSlotSelection = jest.fn();

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('renders without crashing', () => {
        render(
            <BookingDayCards
                weekData={mockWeekData}
                selectedSlots={{}}
                availableSlots={null}
                handleSlotSelection={mockHandleSlotSelection}
            />
        );
    });

    it('renders day cards with correct day names and dates', () => {
        render(
            <BookingDayCards
                weekData={mockWeekData}
                selectedSlots={{}}
                availableSlots={null}
                handleSlotSelection={mockHandleSlotSelection}
            />
        );

        expect(screen.getByText('Wednesday')).toBeInTheDocument();
        expect(screen.getByText('4th Dec')).toBeInTheDocument();
        expect(screen.getByText('Thursday')).toBeInTheDocument();
        expect(screen.getByText('5th Dec')).toBeInTheDocument();
    });

    it('handles week data format with slots for each day', () => {
        const mockAvailableSlots = {
            weekData: {
                '2024-12-04': {
                    slots: [
                        { label: '09:00AM - 10:00AM', capacity_id: 1 },
                        { label: '10:00AM - 11:00AM', capacity_id: 2 }
                    ]
                },
                '2024-12-05': {
                    slots: [
                        { label: '02:00PM - 03:00PM', capacity_id: 3 }
                    ]
                }
            }
        };

        render(
            <BookingDayCards
                weekData={mockWeekData}
                selectedSlots={{}}
                availableSlots={mockAvailableSlots}
                handleSlotSelection={mockHandleSlotSelection}
            />
        );

        // Check if slots for Wednesday are rendered
        expect(screen.getByText('09:00AM - 10:00AM')).toBeInTheDocument();
        expect(screen.getByText('10:00AM - 11:00AM')).toBeInTheDocument();
        
        // Check if slots for Thursday are rendered
        expect(screen.getByText('02:00PM - 03:00PM')).toBeInTheDocument();
    });

    it('handles old format with single slots array', () => {
        const mockAvailableSlots = {
            slots: [
                { label: '09:00AM - 10:00AM', capacity_id: 1 },
                { label: '10:00AM - 11:00AM', capacity_id: 2 }
            ]
        };

        render(
            <BookingDayCards
                weekData={mockWeekData}
                selectedSlots={{}}
                availableSlots={mockAvailableSlots}
                handleSlotSelection={mockHandleSlotSelection}
            />
        );

        // Both days should show the same slots (old format behavior)
        const slot1Elements = screen.getAllByText('09:00AM - 10:00AM');
        const slot2Elements = screen.getAllByText('10:00AM - 11:00AM');
        
        expect(slot1Elements).toHaveLength(2); // One for each day
        expect(slot2Elements).toHaveLength(2); // One for each day
    });

    it('calls handleSlotSelection when slot is clicked', () => {
        const mockAvailableSlots = {
            weekData: {
                '2024-12-04': {
                    slots: [
                        { label: '09:00AM - 10:00AM', capacity_id: 1 }
                    ]
                }
            }
        };

        render(
            <BookingDayCards
                weekData={mockWeekData}
                selectedSlots={{}}
                availableSlots={mockAvailableSlots}
                handleSlotSelection={mockHandleSlotSelection}
            />
        );

        const slotElement = screen.getByText('09:00AM - 10:00AM');
        fireEvent.click(slotElement);

        expect(mockHandleSlotSelection).toHaveBeenCalledWith('2024-12-04', '09:00AM - 10:00AM');
    });

    it('shows selected slots with correct styling', () => {
        const mockAvailableSlots = {
            weekData: {
                '2024-12-04': {
                    slots: [
                        { label: '09:00AM - 10:00AM', capacity_id: 1 }
                    ]
                }
            }
        };

        const mockSelectedSlots = {
            '2024-12-04': ['09:00AM - 10:00AM']
        };

        render(
            <BookingDayCards
                weekData={mockWeekData}
                selectedSlots={mockSelectedSlots}
                availableSlots={mockAvailableSlots}
                handleSlotSelection={mockHandleSlotSelection}
            />
        );

        const slotElement = screen.getByText('09:00AM - 10:00AM').closest('.wy-slot-box');
        expect(slotElement).toHaveClass('wy-slot-box-selected');
    });

    it('handles empty week data gracefully', () => {
        const { container } = render(
            <BookingDayCards
                weekData={null}
                selectedSlots={{}}
                availableSlots={null}
                handleSlotSelection={mockHandleSlotSelection}
            />
        );

        expect(container.firstChild).toBeEmptyDOMElement();
    });

    it('handles days with no available slots', () => {
        const mockAvailableSlots = {
            weekData: {
                '2024-12-04': {
                    slots: []
                },
                '2024-12-05': {
                    slots: [
                        { label: '02:00PM - 03:00PM', capacity_id: 3 }
                    ]
                }
            }
        };

        render(
            <BookingDayCards
                weekData={mockWeekData}
                selectedSlots={{}}
                availableSlots={mockAvailableSlots}
                handleSlotSelection={mockHandleSlotSelection}
            />
        );

        // Wednesday should have no slots
        expect(screen.queryByText('09:00AM - 10:00AM')).not.toBeInTheDocument();
        
        // Thursday should have one slot
        expect(screen.getByText('02:00PM - 03:00PM')).toBeInTheDocument();
    });
});
