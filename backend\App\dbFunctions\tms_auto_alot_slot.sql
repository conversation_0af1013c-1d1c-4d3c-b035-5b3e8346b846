CREATE OR REPLACE FUNCTION public.tms_auto_alot_slot(form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
DECLARE
    -- Bare minimums
    status boolean := false;
    message text := 'Internal_error';
    resp_data json := '{}';
    affected_rows integer;

    -- Input/context
    ip_address_ text;
    user_agent_ text;
    org_id_ integer;
    usr_id_ uuid;

    -- SR details
    srvc_req_id_ bigint;
    srvc_type_id_ integer;
    sr_form_data json;
    sr_pincode text;

    -- Capacity keys
    provider_id_ integer;
    vertical_id_ integer;
    hub_id_ integer;

    -- Resource matching
    resource_pattern text;

    -- Booking computation
    manpower_ integer;
    job_duration_ integer; -- in minutes
    hub_travel_time_ integer := 20; -- default travel buffer
    total_duration_ integer;
    man_minutes_ integer;

    -- Capacity lookup
    cap_rec record;
    remaining_capacity_ integer;

    -- Booking persistence
    booking_ids int[] := '{}';
    first_capacity_id bigint;
    booking_qty_ integer;

    -- For across slots
    man_minutes_left integer;

    -- Timeline
    user_context_json json;
    booking_update_json json;
    booking_details_ jsonb := '{}'::jsonb;
    booking_slots_ jsonb := '{}'::jsonb;

    -- Existing booking check
    has_existing_booking boolean := false;
BEGIN
    -- Extract basic context
    org_id_ := (form_data_->>'org_id')::integer;
    usr_id_ := (form_data_->>'usr_id')::uuid;
    ip_address_ := form_data_->>'ip_address';
    user_agent_ := form_data_->>'user_agent';

    -- Required: srvc_req_details with id
    srvc_req_id_ := (form_data_->'srvc_req_details'->>'id')::bigint;
    IF srvc_req_id_ IS NULL THEN
        RETURN json_build_object('status', false, 'message', 'srvc_req_id missing');
    END IF;

    -- Load service request to get srvc_type_id, form_data, pincode
    SELECT srvc_type_id, form_data
      INTO srvc_type_id_, sr_form_data
      FROM public.cl_tx_srvc_req
     WHERE db_id = srvc_req_id_;

    IF srvc_type_id_ IS NULL THEN
        RETURN json_build_object('status', false, 'message', 'service request not found');
    END IF;

    sr_pincode := sr_form_data->>'cust_pincode';

    -- Derive provider_id, vertical_id, hub_id using helper
    -- tms_get_translated_capacity_keys expects org_id, srvc_type_id, pincode
    PERFORM 1;
    DECLARE
        cap_keys json;
    BEGIN
        cap_keys := tms_get_translated_capacity_keys(json_build_object(
            'org_id', org_id_,
            'srvc_type_id', srvc_type_id_,
            'pincode', sr_pincode
        ));
        provider_id_ := (cap_keys->'data'->>'provider_id')::int;
        vertical_id_ := (cap_keys->'data'->>'vertical_id')::int;
        hub_id_ := (cap_keys->'data'->>'hub_id')::int;
    EXCEPTION WHEN others THEN
        NULL;
    END;

    IF provider_id_ IS NULL OR vertical_id_ IS NULL OR hub_id_ IS NULL THEN
        RETURN json_build_object('status', false, 'message', 'capacity keys not found for SR pincode');
    END IF;

    -- Compute man-minutes from SR form_data (defaults like tms_create_booking)
    manpower_ := COALESCE((sr_form_data->>'6b858839-f154-4573-9e9b-4b01ebeb44a7')::integer, 1);
    job_duration_ := COALESCE((sr_form_data->>'3151dffe-45c3-4c27-9008-0a01c0205374')::integer, 60);
    total_duration_ := job_duration_ + hub_travel_time_;
    man_minutes_ := total_duration_ * manpower_;

    -- If booking exists already, do nothing
    SELECT EXISTS (
        SELECT 1 FROM public.cl_tx_bookings WHERE order_id = srvc_req_id_
    ) INTO has_existing_booking;

    IF has_existing_booking THEN
        RETURN json_build_object('status', true, 'message', 'booking already exists', 'data', json_build_object('srvc_req_id', srvc_req_id_));
    END IF;

    -- Build resource pattern (skill is wildcard)
    resource_pattern := provider_id_::text || '_' || vertical_id_::text || '_%_' || hub_id_::text;

    -- Try to find a single slot with enough capacity
    FOR cap_rec IN
        SELECT db_id, usr_tmzone_day, start_time, end_time, total_cap_in_minutes, booked_cap_in_minutes
          FROM public.cl_tx_capacity
         WHERE resource_id LIKE resource_pattern
           AND utilization < 1
           AND usr_tmzone_day >= (now() at time zone 'utc')::date
         ORDER BY usr_tmzone_day ASC, start_time ASC
    LOOP
        remaining_capacity_ := cap_rec.total_cap_in_minutes - cap_rec.booked_cap_in_minutes;
        IF remaining_capacity_ >= man_minutes_ THEN
            -- Book within this single slot
            booking_qty_ := man_minutes_;

            INSERT INTO public.cl_tx_bookings (capacity_id, order_id, order_label, booked_qty, manpower, c_meta, u_meta)
            VALUES (cap_rec.db_id, srvc_req_id_, 'Service Request', booking_qty_, manpower_,
                    row(ip_address_, user_agent_, now() at time zone 'utc'),
                    row(ip_address_, user_agent_, now() at time zone 'utc'))
            RETURNING db_id INTO first_capacity_id; -- reuse as booking_id temp

            IF first_capacity_id IS NULL THEN
                RETURN json_build_object('status', false, 'message', 'failed_to_create_booking');
            END IF;

            -- Track booking id, real capacity id stored in cap_rec.db_id
            booking_ids := array_append(booking_ids, first_capacity_id::int);
            first_capacity_id := cap_rec.db_id;

            UPDATE public.cl_tx_capacity
               SET booked_cap_in_minutes = booked_cap_in_minutes + booking_qty_,
                   u_meta = row(ip_address_, user_agent_, now() at time zone 'utc')
             WHERE db_id = cap_rec.db_id;

            -- Build booking details JSON (date -> time range string in IST)
            booking_slots_ := jsonb_build_object(
                to_char(cap_rec.usr_tmzone_day, 'YYYY-MM-DD'),
                jsonb_build_array(
                    to_char(cap_rec.start_time AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kolkata', 'HH12:MIam') || ' - ' ||
                    to_char(cap_rec.end_time   AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kolkata', 'HH12:MIam')
                )
            );
            booking_details_ := jsonb_build_object(
                'selected_date', cap_rec.usr_tmzone_day,
                'capacity_id', cap_rec.db_id,
                'booked_slots', booking_slots_
            );

            -- Prepare context and update service request
            user_context_json := json_build_object(
                'org_id', org_id_,
                'usr_id', usr_id_,
                'ip_address', ip_address_,
                'user_agent', user_agent_,
                'srvc_type_id', srvc_type_id_
            );
            booking_update_json := jsonb_set(user_context_json::jsonb, '{booking_id}', to_jsonb(booking_ids), true);
            booking_update_json := jsonb_set(booking_update_json::jsonb, '{capacity_id}', to_jsonb(cap_rec.db_id), true);
            booking_update_json := jsonb_set(booking_update_json::jsonb, '{booking_details}', booking_details_, true);

            PERFORM tms_create_service_request(booking_update_json, srvc_req_id_::integer);
            PERFORM tms_add_to_srvc_req_timeline(srvc_type_id_, srvc_req_id_, 'UPDATE', 'Booking slots done', user_context_json);

            status := true;
            message := 'success';
            resp_data := json_build_object('booking_ids', booking_ids, 'capacity_id', cap_rec.db_id, 'booking_details', booking_details_);
            RETURN json_build_object('status', status, 'message', message, 'data', resp_data);
        END IF;
    END LOOP;

    -- If no single slot can accommodate, allocate across slots on the same resource pattern
    man_minutes_left := man_minutes_;
    FOR cap_rec IN
        SELECT db_id, usr_tmzone_day, start_time, end_time, total_cap_in_minutes, booked_cap_in_minutes
          FROM public.cl_tx_capacity
         WHERE resource_id LIKE resource_pattern
           AND utilization < 1
           AND usr_tmzone_day >= (now() at time zone 'utc')::date
         ORDER BY usr_tmzone_day ASC, start_time ASC
    LOOP
        remaining_capacity_ := cap_rec.total_cap_in_minutes - cap_rec.booked_cap_in_minutes;
        IF remaining_capacity_ > 0 AND man_minutes_left > 0 THEN
            booking_qty_ := LEAST(man_minutes_left, remaining_capacity_);

            INSERT INTO public.cl_tx_bookings (capacity_id, order_id, order_label, booked_qty, manpower, c_meta, u_meta)
            VALUES (cap_rec.db_id, srvc_req_id_, 'Service Request', booking_qty_, manpower_,
                    row(ip_address_, user_agent_, now() at time zone 'utc'),
                    row(ip_address_, user_agent_, now() at time zone 'utc'))
            RETURNING db_id INTO affected_rows; -- reuse to capture booking id

            IF affected_rows IS NOT NULL THEN
                booking_ids := array_append(booking_ids, affected_rows::int);
                UPDATE public.cl_tx_capacity
                   SET booked_cap_in_minutes = booked_cap_in_minutes + booking_qty_,
                       u_meta = row(ip_address_, user_agent_, now() at time zone 'utc')
                 WHERE db_id = cap_rec.db_id;

                -- Build booking details date map (append per date)
                booking_slots_ := booking_details_->'booked_slots';
                IF booking_slots_ IS NULL THEN
                    booking_slots_ := '{}'::jsonb;
                END IF;
                booking_slots_ := jsonb_set(
                    booking_slots_,
                    ARRAY[to_char(cap_rec.usr_tmzone_day, 'YYYY-MM-DD')],
                    coalesce(booking_slots_->to_char(cap_rec.usr_tmzone_day, 'YYYY-MM-DD'), '[]'::jsonb) ||
                        jsonb_build_array(
                            to_char(cap_rec.start_time AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kolkata', 'HH12:MIam') || ' - ' ||
                            to_char(cap_rec.end_time   AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kolkata', 'HH12:MIam')
                        ),
                    true
                );
                booking_details_ := jsonb_set(booking_details_, '{booked_slots}', booking_slots_, true);

                IF first_capacity_id IS NULL THEN
                    first_capacity_id := cap_rec.db_id;
                END IF;

                man_minutes_left := man_minutes_left - booking_qty_;
                IF man_minutes_left <= 0 THEN
                    EXIT;
                END IF;
            END IF;
        END IF;
    END LOOP;

    IF array_length(booking_ids, 1) IS NULL THEN
        message := 'no capacity available';
        RETURN json_build_object('status', false, 'message', message);
    END IF;

    -- Update SR with booking results for across-slot allocation
    user_context_json := json_build_object(
        'org_id', org_id_,
        'usr_id', usr_id_,
        'ip_address', ip_address_,
        'user_agent', user_agent_,
        'srvc_type_id', srvc_type_id_
    );

    booking_update_json := jsonb_set(user_context_json::jsonb, '{booking_id}', to_jsonb(booking_ids), true);
    IF first_capacity_id IS NOT NULL THEN
        booking_update_json := jsonb_set(booking_update_json::jsonb, '{capacity_id}', to_jsonb(first_capacity_id), true);
    END IF;
    booking_update_json := jsonb_set(booking_update_json::jsonb, '{booking_details}', coalesce(booking_details_, '{}'::jsonb), true);

    PERFORM tms_create_service_request(booking_update_json, srvc_req_id_);
    PERFORM tms_add_to_srvc_req_timeline(srvc_type_id_, srvc_req_id_, 'UPDATE', 'Booking slots done', user_context_json);

    -- Sync total booked quantities for this service request
    PERFORM tms_hlpr_sync_booked_man_minutes_on_srvc_req(srvc_req_id_::bigint);

    status := true;
    message := 'success';
    resp_data := json_build_object('booking_ids', booking_ids, 'capacity_id', first_capacity_id, 'booking_details', booking_details_);
    RETURN json_build_object('status', status, 'message', message, 'data', resp_data);
END;
$function$
;
