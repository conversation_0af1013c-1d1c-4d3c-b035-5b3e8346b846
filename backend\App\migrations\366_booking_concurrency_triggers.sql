-- Migration: Add booking concurrency control triggers
-- This migration adds triggers to prevent concurrent booking conflicts

BEGIN;

-- Source the trigger functions and triggers
\i backend/App/dbFunctions/booking_concurrency_triggers.sql

-- Add missing column if not exists (for cancelled bookings)
ALTER TABLE public.cl_tx_bookings 
ADD COLUMN IF NOT EXISTS is_cancelled boolean DEFAULT false;

-- Add index for better performance on cancelled bookings
CREATE INDEX IF NOT EXISTS cl_tx_bookings_is_cancelled_idx 
ON public.cl_tx_bookings (is_cancelled);

-- Update existing bookings to set is_cancelled = false where NULL
UPDATE public.cl_tx_bookings 
SET is_cancelled = false 
WHERE is_cancelled IS NULL;

-- Make is_cancelled NOT NULL
ALTER TABLE public.cl_tx_bookings 
ALTER COLUMN is_cancelled SET NOT NULL;

-- Add constraint to ensure booked_qty is positive for non-cancelled bookings
ALTER TABLE public.cl_tx_bookings 
DROP CONSTRAINT IF EXISTS cl_tx_bookings_booked_qty_positive_when_active;

ALTER TABLE public.cl_tx_bookings 
ADD CONSTRAINT cl_tx_bookings_booked_qty_positive_when_active 
CHECK (is_cancelled = true OR booked_qty > 0);

COMMIT;
