-- =====================================================
-- BOOKING CONCURRENCY CONTROL TEST SCRIPT
-- =====================================================
-- This script tests the concurrency control mechanisms
-- Run this script to verify triggers and locking work correctly

BEGIN;

-- =====================================================
-- TEST DATA SETUP
-- =====================================================

-- Create test capacity slot
INSERT INTO cl_tx_capacity (
    usr_tmzone_day,
    resource_id,
    resource_label,
    start_time,
    end_time,
    total_capacity,
    available_capacity,
    booked_cap_in_minutes,
    total_cap_in_minutes,
    c_meta,
    u_meta
) VALUES (
    CURRENT_DATE + INTERVAL '1 day',
    'TEST_RESOURCE_001',
    'Test Resource for Concurrency',
    (CURRENT_DATE + INTERVAL '1 day' + INTERVAL '10 hours')::timestamp,
    (CURRENT_DATE + INTERVAL '1 day' + INTERVAL '11 hours')::timestamp,
    2,
    2,
    0,
    120, -- 2 people * 60 minutes = 120 man-minutes
    row('127.0.0.1', 'test-agent', now() at time zone 'utc'),
    row('127.0.0.1', 'test-agent', now() at time zone 'utc')
) ON CONFLICT (resource_id, start_time, end_time) DO NOTHING;

-- Get the test capacity ID
DO $$
DECLARE
    test_capacity_id bigint;
    test_service_req_id bigint;
BEGIN
    -- Get capacity ID
    SELECT db_id INTO test_capacity_id
    FROM cl_tx_capacity
    WHERE resource_id = 'TEST_RESOURCE_001'
    AND usr_tmzone_day = CURRENT_DATE + INTERVAL '1 day'
    LIMIT 1;

    -- Create test service request if needed
    INSERT INTO cl_tx_srvc_req (
        org_id,
        srvc_type_id,
        display_code,
        form_data,
        c_meta,
        u_meta
    ) VALUES (
        1,
        1,
        'TEST-BOOKING-001',
        '{"test": true}',
        row('127.0.0.1', 'test-agent', now() at time zone 'utc'),
        row('127.0.0.1', 'test-agent', now() at time zone 'utc')
    ) ON CONFLICT DO NOTHING
    RETURNING db_id INTO test_service_req_id;

    -- If no new record was created, get existing one
    IF test_service_req_id IS NULL THEN
        SELECT db_id INTO test_service_req_id
        FROM cl_tx_srvc_req
        WHERE display_code = 'TEST-BOOKING-001'
        LIMIT 1;
    END IF;

    RAISE NOTICE 'Test setup complete. Capacity ID: %, Service Request ID: %', test_capacity_id, test_service_req_id;
END $$;

-- =====================================================
-- TEST 1: Normal Booking Creation
-- =====================================================

DO $$
DECLARE
    test_capacity_id bigint;
    test_service_req_id bigint;
    booking_result json;
BEGIN
    -- Get test IDs
    SELECT db_id INTO test_capacity_id FROM cl_tx_capacity WHERE resource_id = 'TEST_RESOURCE_001' LIMIT 1;
    SELECT db_id INTO test_service_req_id FROM cl_tx_srvc_req WHERE display_code = 'TEST-BOOKING-001' LIMIT 1;

    RAISE NOTICE 'TEST 1: Creating normal booking...';
    
    -- Test normal booking creation
    SELECT tms_create_booking_v2(
        test_service_req_id,
        json_build_object(
            'org_id', 1,
            'usr_id', '00000000-0000-0000-0000-000000000001',
            'ip_address', '127.0.0.1',
            'user_agent', 'test-agent',
            'srvc_type_id', 1,
            'booking_data', json_build_object(
                'capacity_id', test_capacity_id,
                'selected_date', CURRENT_DATE + INTERVAL '1 day'
            ),
            '6b858839-f154-4573-9e9b-4b01ebeb44a7', 1  -- manpower field
        )
    ) INTO booking_result;

    RAISE NOTICE 'Booking result: %', booking_result;
    
    IF (booking_result->>'status')::boolean THEN
        RAISE NOTICE 'TEST 1 PASSED: Normal booking created successfully';
    ELSE
        RAISE NOTICE 'TEST 1 FAILED: %', booking_result->>'message';
    END IF;
END $$;

-- =====================================================
-- TEST 2: Overbooking Prevention
-- =====================================================

DO $$
DECLARE
    test_capacity_id bigint;
    current_booked integer;
    total_capacity integer;
BEGIN
    -- Get test capacity info
    SELECT db_id, booked_cap_in_minutes, total_cap_in_minutes 
    INTO test_capacity_id, current_booked, total_capacity
    FROM cl_tx_capacity 
    WHERE resource_id = 'TEST_RESOURCE_001' 
    LIMIT 1;

    RAISE NOTICE 'TEST 2: Testing overbooking prevention...';
    RAISE NOTICE 'Current capacity - Booked: %, Total: %', current_booked, total_capacity;

    -- Try to manually overbook (should fail)
    BEGIN
        UPDATE cl_tx_capacity 
        SET booked_cap_in_minutes = total_cap_in_minutes + 60  -- Overbook by 60 minutes
        WHERE db_id = test_capacity_id;
        
        RAISE NOTICE 'TEST 2 FAILED: Overbooking was allowed!';
    EXCEPTION 
        WHEN check_violation THEN
            RAISE NOTICE 'TEST 2 PASSED: Overbooking prevented by trigger - %', SQLERRM;
        WHEN OTHERS THEN
            RAISE NOTICE 'TEST 2 UNEXPECTED ERROR: %', SQLERRM;
    END;
END $$;

-- =====================================================
-- TEST 3: Booking Validation Trigger
-- =====================================================

DO $$
DECLARE
    test_capacity_id bigint;
    test_service_req_id bigint;
    remaining_capacity integer;
BEGIN
    -- Get test IDs and remaining capacity
    SELECT c.db_id, c.total_cap_in_minutes - c.booked_cap_in_minutes
    INTO test_capacity_id, remaining_capacity
    FROM cl_tx_capacity c
    WHERE resource_id = 'TEST_RESOURCE_001' 
    LIMIT 1;

    SELECT db_id INTO test_service_req_id FROM cl_tx_srvc_req WHERE display_code = 'TEST-BOOKING-001' LIMIT 1;

    RAISE NOTICE 'TEST 3: Testing booking validation trigger...';
    RAISE NOTICE 'Remaining capacity: % minutes', remaining_capacity;

    -- Try to create booking larger than remaining capacity
    BEGIN
        INSERT INTO cl_tx_bookings (
            capacity_id,
            order_id,
            order_label,
            booked_qty,
            manpower,
            is_cancelled,
            c_meta,
            u_meta
        ) VALUES (
            test_capacity_id,
            test_service_req_id,
            'TEST-OVERBOOK',
            remaining_capacity + 30,  -- Try to book more than available
            1,
            false,
            row('127.0.0.1', 'test-agent', now() at time zone 'utc'),
            row('127.0.0.1', 'test-agent', now() at time zone 'utc')
        );
        
        RAISE NOTICE 'TEST 3 FAILED: Overbooking validation did not trigger!';
    EXCEPTION 
        WHEN check_violation THEN
            RAISE NOTICE 'TEST 3 PASSED: Booking validation prevented overbooking - %', SQLERRM;
        WHEN OTHERS THEN
            RAISE NOTICE 'TEST 3 UNEXPECTED ERROR: %', SQLERRM;
    END;
END $$;

-- =====================================================
-- TEST 4: Concurrent Booking Simulation
-- =====================================================

DO $$
DECLARE
    test_capacity_id bigint;
    remaining_capacity integer;
BEGIN
    -- Get remaining capacity
    SELECT c.db_id, c.total_cap_in_minutes - c.booked_cap_in_minutes
    INTO test_capacity_id, remaining_capacity
    FROM cl_tx_capacity c
    WHERE resource_id = 'TEST_RESOURCE_001' 
    LIMIT 1;

    RAISE NOTICE 'TEST 4: Concurrent booking simulation...';
    RAISE NOTICE 'This test requires manual execution in separate sessions to test locking';
    RAISE NOTICE 'Remaining capacity: % minutes', remaining_capacity;
    RAISE NOTICE 'To test concurrency:';
    RAISE NOTICE '1. Open two database sessions';
    RAISE NOTICE '2. In each session, run a booking for capacity_id: %', test_capacity_id;
    RAISE NOTICE '3. One should succeed, one should wait or fail appropriately';
END $$;

-- =====================================================
-- CLEANUP
-- =====================================================

-- Clean up test data
DELETE FROM cl_tx_bookings WHERE order_label LIKE 'TEST-%';
DELETE FROM cl_tx_srvc_req WHERE display_code LIKE 'TEST-BOOKING-%';
DELETE FROM cl_tx_capacity WHERE resource_id = 'TEST_RESOURCE_001';

RAISE NOTICE 'Test cleanup completed';

COMMIT;

-- =====================================================
-- MANUAL CONCURRENCY TEST INSTRUCTIONS
-- =====================================================

/*
To manually test concurrency control:

1. Create test data:
   INSERT INTO cl_tx_capacity (...) -- Create a test slot

2. Open two separate database sessions

3. In Session 1, start a transaction and run:
   BEGIN;
   SELECT * FROM cl_tx_capacity WHERE resource_id = 'TEST_RESOURCE_001' FOR UPDATE;
   -- Don't commit yet

4. In Session 2, try to book the same slot:
   SELECT tms_create_booking_v2(...);
   -- This should wait for Session 1's lock

5. Commit Session 1:
   COMMIT;
   -- Session 2 should now proceed

6. Verify only one booking succeeded and capacity is correctly updated

Expected Results:
- Session 2 waits for Session 1 to complete
- Only one booking succeeds if capacity is insufficient for both
- No overbooking occurs
- Capacity is accurately maintained
*/
