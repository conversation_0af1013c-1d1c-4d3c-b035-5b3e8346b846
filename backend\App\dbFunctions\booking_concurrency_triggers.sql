-- =====================================================
-- BOOKING CONCURRENCY CONTROL TRIGGERS
-- =====================================================
-- This file contains triggers to prevent concurrent booking conflicts
-- and ensure data consistency during booking operations

BEGIN;

-- =====================================================
-- TRIGGER FUNCTION: Prevent Overbooking on Capacity Updates
-- =====================================================
CREATE OR REPLACE FUNCTION public.trg_prevent_capacity_overbooking()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $function$
DECLARE
    current_utilization numeric;
    new_utilization numeric;
BEGIN
    -- Calculate new utilization after the update
    IF NEW.total_cap_in_minutes > 0 THEN
        new_utilization := NEW.booked_cap_in_minutes::numeric / NEW.total_cap_in_minutes::numeric;
    ELSE
        new_utilization := 0;
    END IF;
    
    -- Prevent overbooking (utilization > 1.0)
    IF new_utilization > 1.0 THEN
        RAISE EXCEPTION 'Booking would exceed capacity. Current booked: % minutes, Total capacity: % minutes, Utilization would be: %', 
            NEW.booked_cap_in_minutes, NEW.total_cap_in_minutes, new_utilization
            USING ERRCODE = 'check_violation',
                  HINT = 'This slot is already fully booked or would be overbooked by this operation';
    END IF;
    
    -- Log the capacity change for debugging
    RAISE NOTICE 'Capacity updated for slot %. Booked: % minutes, Total: % minutes, Utilization: %', 
        NEW.db_id, NEW.booked_cap_in_minutes, NEW.total_cap_in_minutes, new_utilization;
    
    RETURN NEW;
END;
$function$;

-- =====================================================
-- TRIGGER FUNCTION: Validate Booking Creation
-- =====================================================
CREATE OR REPLACE FUNCTION public.trg_validate_booking_creation()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $function$
DECLARE
    capacity_info record;
    current_booked_minutes integer;
    remaining_capacity integer;
BEGIN
    -- Get current capacity information with lock
    SELECT 
        total_cap_in_minutes, 
        booked_cap_in_minutes, 
        available_capacity,
        resource_id,
        start_time,
        end_time
    INTO capacity_info
    FROM cl_tx_capacity 
    WHERE db_id = NEW.capacity_id
    FOR UPDATE;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Capacity slot with ID % not found', NEW.capacity_id
            USING ERRCODE = 'foreign_key_violation';
    END IF;
    
    -- Calculate remaining capacity
    remaining_capacity := capacity_info.total_cap_in_minutes - capacity_info.booked_cap_in_minutes;
    
    -- Check if there's enough capacity for this booking
    IF NEW.booked_qty > remaining_capacity THEN
        RAISE EXCEPTION 'Insufficient capacity for booking. Requested: % minutes, Available: % minutes for slot % (% to %)', 
            NEW.booked_qty, remaining_capacity, NEW.capacity_id, 
            capacity_info.start_time, capacity_info.end_time
            USING ERRCODE = 'check_violation',
                  HINT = 'This time slot does not have enough available capacity';
    END IF;
    
    -- Log successful booking validation
    RAISE NOTICE 'Booking validated for capacity %. Booking qty: % minutes, Remaining after: % minutes', 
        NEW.capacity_id, NEW.booked_qty, remaining_capacity - NEW.booked_qty;
    
    RETURN NEW;
END;
$function$;

-- =====================================================
-- TRIGGER FUNCTION: Auto-update Capacity on Booking Changes
-- =====================================================
CREATE OR REPLACE FUNCTION public.trg_auto_update_capacity_on_booking()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $function$
DECLARE
    capacity_change integer := 0;
BEGIN
    -- Handle INSERT
    IF TG_OP = 'INSERT' THEN
        IF NOT NEW.is_cancelled THEN
            capacity_change := NEW.booked_qty;
        END IF;
    -- Handle UPDATE
    ELSIF TG_OP = 'UPDATE' THEN
        -- Calculate the net change in booked quantity
        IF OLD.is_cancelled AND NOT NEW.is_cancelled THEN
            -- Booking was reactivated
            capacity_change := NEW.booked_qty;
        ELSIF NOT OLD.is_cancelled AND NEW.is_cancelled THEN
            -- Booking was cancelled
            capacity_change := -OLD.booked_qty;
        ELSIF NOT OLD.is_cancelled AND NOT NEW.is_cancelled THEN
            -- Booking quantity changed
            capacity_change := NEW.booked_qty - OLD.booked_qty;
        END IF;
    -- Handle DELETE
    ELSIF TG_OP = 'DELETE' THEN
        IF NOT OLD.is_cancelled THEN
            capacity_change := -OLD.booked_qty;
        END IF;
    END IF;
    
    -- Update capacity if there's a change
    IF capacity_change != 0 THEN
        UPDATE cl_tx_capacity 
        SET booked_cap_in_minutes = booked_cap_in_minutes + capacity_change,
            u_meta = row('system_trigger', 'booking_trigger', now() at time zone 'utc')
        WHERE db_id = COALESCE(NEW.capacity_id, OLD.capacity_id);
        
        RAISE NOTICE 'Capacity auto-updated for slot %. Change: % minutes', 
            COALESCE(NEW.capacity_id, OLD.capacity_id), capacity_change;
    END IF;
    
    -- Return appropriate record based on operation
    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$function$;

-- =====================================================
-- CREATE TRIGGERS
-- =====================================================

-- Drop existing triggers if they exist
DROP TRIGGER IF EXISTS trg_capacity_overbooking_check ON public.cl_tx_capacity;
DROP TRIGGER IF EXISTS trg_booking_validation ON public.cl_tx_bookings;
DROP TRIGGER IF EXISTS trg_booking_capacity_sync ON public.cl_tx_bookings;

-- Trigger to prevent overbooking on capacity updates
CREATE TRIGGER trg_capacity_overbooking_check
    BEFORE UPDATE ON public.cl_tx_capacity
    FOR EACH ROW
    WHEN (OLD.booked_cap_in_minutes IS DISTINCT FROM NEW.booked_cap_in_minutes)
    EXECUTE FUNCTION public.trg_prevent_capacity_overbooking();

-- Trigger to validate booking creation
CREATE TRIGGER trg_booking_validation
    BEFORE INSERT ON public.cl_tx_bookings
    FOR EACH ROW
    EXECUTE FUNCTION public.trg_validate_booking_creation();

-- Trigger to auto-update capacity when bookings change (optional - can be disabled if manual updates are preferred)
-- CREATE TRIGGER trg_booking_capacity_sync
--     AFTER INSERT OR UPDATE OR DELETE ON public.cl_tx_bookings
--     FOR EACH ROW
--     EXECUTE FUNCTION public.trg_auto_update_capacity_on_booking();

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Ensure we have proper indexes for locking performance
CREATE INDEX IF NOT EXISTS cl_tx_capacity_db_id_utilization_idx 
ON public.cl_tx_capacity (db_id, utilization) 
WHERE utilization < 1;

CREATE INDEX IF NOT EXISTS cl_tx_bookings_capacity_id_cancelled_idx 
ON public.cl_tx_bookings (capacity_id, is_cancelled);

-- =====================================================
-- COMMENTS
-- =====================================================

COMMENT ON FUNCTION public.trg_prevent_capacity_overbooking() IS 
'Trigger function to prevent capacity overbooking by checking utilization before updates';

COMMENT ON FUNCTION public.trg_validate_booking_creation() IS 
'Trigger function to validate booking creation and ensure sufficient capacity exists';

COMMENT ON FUNCTION public.trg_auto_update_capacity_on_booking() IS 
'Trigger function to automatically update capacity when bookings are created, updated, or deleted';

COMMIT;
