const nodemailer = require('nodemailer');

const performJob = async (job, done) => {
    const app = require('../../../app');
    const mainDb = app.get('db');
    if (job.data) {
        try {
            const sendEMAILResult = await performSendEMAIL(job.data);
            //add a log for send_email
            let notify_info = job.data;
            const addLogRes = await addLog(
                mainDb,
                notify_info,
                sendEMAILResult
            );
        } catch (error) {
            console.log(error);
            return;
        }
    } else {
        console.error('Invalid send email call', job.data);
    }
    done(null, {});
};

const emailSubjectPrefix = `[TMS]`;

const performSendEMAIL = async (data) => {
    let to = '<EMAIL>';
    let subject = `${emailSubjectPrefix} ${data?.subject}`;
    let messageHTML = data?.message;
    //optional param for save email_log
    let cc = data?.cc || '';
    let bcc = data?.bcc || '';
    let attachments = data?.attachments || '';

    if (process.env.NODE_ENV == undefined) {
        to = '<EMAIL>';
        cc = '';
        bcc = '';
    }

    let email_credential = getEmailCredentials();
    var smtpTransport = nodemailer.createTransport({
        host: email_credential.EMAIL_SMTP_HOST,
        port: email_credential.EMAIL_SMTP_PORT,
        auth: {
            user: email_credential.EMAIL_AUTH_USER,
            pass: email_credential.EMAIL_AUTH_PASS,
        },
    });

    const notifyData = {
        from: `WIFY - Do Not Reply <${email_credential.EMAIL_FROM}>`,
        to: to,
        cc: cc,
        bcc: bcc,
        subject: subject,
        html: messageHTML,
        attachments: attachments,
    };

    try {
        // send email
        let sendEmailResp = await smtpTransport.sendMail(notifyData);
        console.log('Email sent successfully');
        console.log('To Email ids', to);
        return {
            response: sendEmailResp,
            status: 'Success',
        };
    } catch (error) {
        console.log('Email send error resp ==>', error);
        return {
            response: error,
            status: 'Failed',
        };
    }
};

const addLog = async (db, notify_info, send_email_result) => {
    let email_credential = getEmailCredentials();
    let query = {
        ...notify_info,
        ...send_email_result,
    };
    query['server_host'] = email_credential.EMAIL_SMTP_HOST;
    query['email_from'] = email_credential.EMAIL_AUTH_USER;
    let form_data = JSON.stringify(query);

    // console.log("addLog form_data",form_data);
    if (!db) {
        console.log('EMAIL log DB not found');
        return;
    }
    try {
        let emailLogResp = (await db.tms_add_email_log(form_data))?.[0]
            .tms_add_email_log;
        if (!emailLogResp.status) {
            console.log('EMAIL log addition failed', emailLogResp);
            return;
        } else {
            console.log('EMAIL log addition successful');
            return emailLogResp;
        }
    } catch (error) {
        console.log('EMAIL log addition failed 2', error);
        return;
    }
};

const getEmailCredentials = () => {
    return {
        EMAIL_SMTP_HOST: process.env.EMAIL_SMTP_HOST || 'smtp.gmail.com',
        EMAIL_SMTP_PORT: process.env.EMAIL_SMTP_PORT || 465,
        EMAIL_AUTH_USER: process.env.EMAIL_AUTH_USER || '<EMAIL>',
        EMAIL_AUTH_PASS: process.env.EMAIL_AUTH_PASS || '9967512263',
        EMAIL_FROM: process.env.EMAIL_FROM || '<EMAIL>',
    };
};

exports.default = performJob;
