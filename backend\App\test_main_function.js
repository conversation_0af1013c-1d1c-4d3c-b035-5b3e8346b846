const massive = require('massive');
require('dotenv').config();

async function testMainFunction() {
    try {
        // Connect to database
        const db = await massive(process.env.DB_URL);
        console.log('Connected to database');

        console.log('\n=== Testing tms_ace_get_capacity_data_by_resource with total_cap_man_minutes ===');
        
        // Test the main function with sample parameters
        try {
            const capacityResult = await db.query(`
                SELECT * FROM tms_ace_get_capacity_data_by_resource($1, $2, $3, $4, $5)
                LIMIT 3
            `, [1, 3, 5, '2', '2024-12-04']);
            
            if (capacityResult.length > 0) {
                console.log(`Found ${capacityResult.length} capacity records`);
                console.log('\nSample capacity data with total_cap_man_minutes:');
                capacityResult.forEach((record, index) => {
                    console.log(`\nRecord ${index + 1}:`);
                    console.log(`  Resource ID: ${record.resource_id}`);
                    console.log(`  Start Time: ${record.start_time}`);
                    console.log(`  End Time: ${record.end_time}`);
                    console.log(`  Total Capacity: ${record.total_capacity}`);
                    console.log(`  Available Capacity: ${record.available_capacity}`);
                    console.log(`  Booked Cap in Minutes: ${record.booked_cap_in_minutes}`);
                    console.log(`  Total Cap Man Minutes: ${record.total_cap_man_minutes}`);
                });
            } else {
                console.log('No capacity data found for the test parameters');
                console.log('Trying with different parameters...');
                
                // Try with different parameters
                const capacityResult2 = await db.query(`
                    SELECT * FROM tms_ace_get_capacity_data_by_resource($1, $2, $3, $4, $5)
                    LIMIT 3
                `, [1, 1, 1, '1', '2024-12-04']);
                
                if (capacityResult2.length > 0) {
                    console.log(`Found ${capacityResult2.length} capacity records with different parameters`);
                    console.log('\nSample capacity data:');
                    capacityResult2.forEach((record, index) => {
                        console.log(`\nRecord ${index + 1}:`);
                        console.log(`  Resource ID: ${record.resource_id}`);
                        console.log(`  Total Cap Man Minutes: ${record.total_cap_man_minutes}`);
                    });
                } else {
                    console.log('No capacity data found with alternative parameters either');
                }
            }
        } catch (error) {
            console.log('Error testing main function:', error.message);
        }

        console.log('\n=== Testing helper function directly ===');
        
        // Test the helper function with a simple case
        try {
            const helperResult = await db.query(`
                SELECT tms_hlpr_calculate_total_cap_man_minutes(
                    '550e8400-e29b-41d4-a716-************'::uuid, 
                    1, 
                    '2024-12-04 13:00:00+00'::timestamp with time zone, 
                    '2024-12-04 14:00:00+00'::timestamp with time zone
                ) as result
            `);
            
            console.log(`Helper function result (no availability data): ${helperResult[0].result} minutes`);
        } catch (error) {
            console.log('Error testing helper function:', error.message);
        }

        console.log('\n=== Checking if bulk upsert function includes new column ===');
        
        // Test if the bulk upsert function can handle the new column
        try {
            const testData = [{
                resourceId: 'test_1_1_1_1',
                startTime: '2024-12-04 13:00:00',
                endTime: '2024-12-04 14:00:00',
                totalCapacity: 5,
                availableCapacity: 3,
                bookedCapacity: 0.5,
                totalCapManMinutes: 120.5,
                organizationName: 'Test Org',
                metadata: {
                    verticalName: 'Test Vertical',
                    skillName: 'Test Skill',
                    hubCode: 'TEST'
                }
            }];

            const bulkResult = await db.query(`
                SELECT * FROM tms_ace_bulk_upsert_capacity_data($1, $2, $3, $4)
            `, [JSON.stringify(testData), 1, '127.0.0.1', 'test-agent']);
            
            console.log('Bulk upsert test result:', JSON.stringify(bulkResult[0], null, 2));
        } catch (error) {
            console.log('Error testing bulk upsert function:', error.message);
        }

        console.log('\nTest completed successfully!');
        
    } catch (error) {
        console.error('Error:', error);
    } finally {
        process.exit(0);
    }
}

testMainFunction();
