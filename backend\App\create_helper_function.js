const massive = require('massive');
const fs = require('fs');
require('dotenv').config();

async function createHelperFunction() {
    try {
        // Connect to database
        const db = await massive(process.env.DB_URL);
        console.log('Connected to database');

        // Read the helper function SQL file
        const sqlContent = fs.readFileSync('./dbFunctions/tms_hlpr_calculate_total_cap_man_minutes.sql', 'utf8');
        console.log('Read SQL file content');

        // Execute the SQL to create the function
        await db.query(sqlContent);
        console.log('Helper function created successfully!');

        // Also run the migration to add the column
        const migrationContent = fs.readFileSync('./migrations/360_add_total_cap_man_minutes_column.sql', 'utf8');
        console.log('Read migration file content');

        await db.query(migrationContent);
        console.log('Migration executed successfully!');

        console.log('All setup completed successfully!');
        
    } catch (error) {
        console.error('Error:', error);
    } finally {
        process.exit(0);
    }
}

createHelperFunction();
