CREATE OR REPLACE FUNCTION public.tms_hlpr_is_booking_available(form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
DECLARE
    -- Response variables
    status boolean := false;
    message text := 'Internal_error';
    resp_data json;
    
    -- Input parameters from form_data
    org_id_ integer;
    usr_id_ uuid;
    ip_address_ text;
    user_agent_ text;
    capacity_id_ bigint default null;
    pincode_ text;
    vertical_id_ integer;
    slot_day_ date default null;
    slot_time_ time;
    service_hub text;
    skill_id_ int := 1;

    -- Booking calculation variables
    manpower_ integer;
    job_duration_ integer := 60; -- default 60 minutes
    hub_travel_time_ integer := 20; -- default 20 minutes
    total_duration_ integer;
    required_man_minutes_ integer;
        
    -- Capacity variables
    available_capacity_ integer;
    total_cap_in_minutes_ integer;
    booked_cap_in_minutes_ integer;
    remaining_capacity_ integer;

    -- For base/next slot comparisons
    cur_day_ date;
    cur_start_time_ time;
    next_db_id_ bigint;
    next_avail_ integer := 0;
    sum_avail_ integer := 0;
    
    -- Resource lookup variables
    provider_id_ integer;
    hub_id_ integer;
    resource_id_ text;
    resource_pattern_ text;
    
    -- Capacity lookup variables
    found_capacity_id_ bigint;
    cap_rec record;
BEGIN
    -- Extract basic parameters from form_data
    org_id_ := (form_data_->>'org_id')::integer;
    usr_id_ := (form_data_->>'usr_id')::uuid;
    ip_address_ := form_data_->>'ip_address';
    user_agent_ := form_data_->>'user_agent';

    -- Extract booking parameters
    capacity_id_ := (form_data_->'booking_data'->>'capacity_id')::bigint;
    pincode_ := form_data_->>'cust_pincode';
    vertical_id_ := (form_data_->>'vertical_id')::integer;
    provider_id_ = form_data_->>'new_prvdr';

    slot_day_ := (
        SELECT MIN(key)::date
        FROM jsonb_object_keys((form_data_->'booking_data'->'booked_slots')::jsonb) key
    );
    IF slot_day_ IS NULL THEN 
        slot_day_ := (form_data_->>'bulk_booking_day')::date;  -- CHANGED: cast defensively
    END IF;
    slot_time_ := (form_data_->>'slot_time')::time;
    raise notice 'slot_day_%',slot_day_;
    -- Calculate required man-minutes
    manpower_ := COALESCE((form_data_->>'6b858839-f154-4573-9e9b-4b01ebeb44a7')::integer, 1);
    job_duration_ := COALESCE((form_data_->>'3151dffe-45c3-4c27-9008-0a01c0205374')::integer, 10);
    hub_travel_time_ := COALESCE((form_data_->>'hub_travel_time')::integer, 20);
    total_duration_ := job_duration_ + hub_travel_time_;
    required_man_minutes_ := total_duration_ * manpower_;

    -- SCENARIO 1: capacity_id is provided
    IF capacity_id_ IS NOT NULL THEN
        -- Include day & start_time so we can find the immediate next slot for the same resource
        SELECT available_capacity, total_cap_in_minutes, booked_cap_in_minutes, resource_id,
               usr_tmzone_day, start_time::time
          INTO available_capacity_, total_cap_in_minutes_, booked_cap_in_minutes_, resource_id_,
               cur_day_, cur_start_time_
          FROM cl_tx_capacity
         WHERE db_id = capacity_id_
           AND available_capacity > 0
           AND utilization < 1;

        IF FOUND THEN
            remaining_capacity_ := total_cap_in_minutes_ - booked_cap_in_minutes_;

            -- Single-slot check
            IF required_man_minutes_ <= remaining_capacity_ THEN
                status := true;
                message := 'Booking available in single slot';
                found_capacity_id_ := capacity_id_;
            ELSE
                -- CHANGED: Only consider the NEXT immediate slot for the SAME resource
                SELECT db_id,
                       (total_cap_in_minutes - booked_cap_in_minutes) AS avail
                  INTO next_db_id_, next_avail_
                  FROM cl_tx_capacity
                 WHERE resource_id = resource_id_
                   AND available_capacity > 0
                   AND utilization < 1
                   AND booked_cap_in_minutes < total_cap_in_minutes
                   AND (
                        (usr_tmzone_day, start_time::time) >
                        (cur_day_, cur_start_time_::time)
                       )
                 ORDER BY usr_tmzone_day ASC, start_time ASC
                 LIMIT 1;

                sum_avail_ := remaining_capacity_ + COALESCE(next_avail_, 0);

                IF sum_avail_ >= required_man_minutes_ THEN
                    status := true;
                    message := 'Booking available across two consecutive slots (current + next)';
                    found_capacity_id_ := capacity_id_; -- starting slot
                ELSE
                   --Booking failed - Insufficient capacity across current + next slot
                    message := 'Booking failed - Insufficient capacity';
                END IF;
            END IF;
        ELSE
            message := 'Booking failed - No available capacity or fully utilized';
        END IF;

    -- SCENARIO 2: capacity_id not provided, use pincode/org/vertical
    ELSE
        IF pincode_ IS NOT NULL THEN
            SELECT hub.id
              INTO service_hub
              FROM cl_tx_vertical_srvc_hubs AS hub
             WHERE pincode_ = ANY(hub.pincodes)
               AND hub.is_active = TRUE
               AND hub.vertical_id = vertical_id_;

            IF service_hub IS NOT NULL THEN
                hub_id_ := service_hub::int;
            END IF;  
            
            IF vertical_id_ IS NULL OR hub_id_ IS NULL THEN
                message := 'Booking failed - Could not determine provider/vertical/hub from pincode';
                RETURN json_build_object('status', false, 'message', message,
                                         'data', json_build_object(
                                             'booking_available', false,
                                             'required_man_minutes', required_man_minutes_,
                                             'message', message
                                         ));
            END IF;

            -- Build resource pattern (skill is wildcard in your scheme)
            resource_pattern_ := provider_id_::text || '_' || vertical_id_::text || '_' || skill_id_::text || '_' || hub_id_::text;
            raise notice 'resource_pattern_ %',resource_pattern_;
            -- SCENARIO 2a: slot_day and slot_time both provided
            IF slot_day_ IS NOT NULL AND slot_time_ IS NOT NULL THEN
                -- Pick the exact requested slot (highest remaining if multiple rows at same time)
                SELECT db_id, total_cap_in_minutes, booked_cap_in_minutes, resource_id
                  INTO found_capacity_id_, total_cap_in_minutes_, booked_cap_in_minutes_, resource_id_
                  FROM cl_tx_capacity
                 WHERE resource_id LIKE resource_pattern_
                   AND usr_tmzone_day = slot_day_
                   AND start_time::time = slot_time_
                   AND utilization < 1
                   AND available_capacity > 0
                 ORDER BY (total_cap_in_minutes - booked_cap_in_minutes) DESC
                 LIMIT 1;

                IF FOUND THEN
                    remaining_capacity_ := total_cap_in_minutes_ - booked_cap_in_minutes_;

                    IF required_man_minutes_ <= remaining_capacity_ THEN
                        status := true;
                        message := 'Booking available for specific slot day and time';
                    ELSE
                        -- CHANGED: Only check the immediate next slot for the SAME resource
                        SELECT db_id,
                               (total_cap_in_minutes - booked_cap_in_minutes) AS avail
                          INTO next_db_id_, next_avail_
                          FROM cl_tx_capacity
                         WHERE resource_id = resource_id_
                           AND available_capacity > 0
                           AND utilization < 1
                           AND booked_cap_in_minutes < total_cap_in_minutes
                           AND (
                                (usr_tmzone_day, start_time::time) >
                                (slot_day_, slot_time_::time)
                               )
                         ORDER BY usr_tmzone_day ASC, start_time ASC
                         LIMIT 1;

                        sum_avail_ := remaining_capacity_ + COALESCE(next_avail_, 0);

                        IF sum_avail_ >= required_man_minutes_ THEN
                            status := true;
                            message := 'Booking available across two consecutive slots (requested + next)';
                        ELSE
                             --Booking failed - Insufficient capacity across current + next slot
                            message := 'Booking failed - Insufficient capacity for requested + next slot';
                        END IF;
                    END IF;
                ELSE
                    message := 'Booking failed - No capacity found for specific slot day and time';
                END IF;

            -- SCENARIO 2b: only slot_day provided
            ELSIF slot_day_ IS NOT NULL THEN
                -- First try to find a single slot with enough capacity
                SELECT db_id, total_cap_in_minutes, booked_cap_in_minutes, resource_id, start_time::time
                  INTO found_capacity_id_, total_cap_in_minutes_, booked_cap_in_minutes_, resource_id_, cur_start_time_
                  FROM cl_tx_capacity
                 WHERE resource_id LIKE resource_pattern_
                   AND usr_tmzone_day = slot_day_
                   AND utilization < 1
                   AND available_capacity > 0
                   AND (total_cap_in_minutes - booked_cap_in_minutes) >= required_man_minutes_
                 ORDER BY (total_cap_in_minutes - booked_cap_in_minutes) DESC
                 LIMIT 1;

                IF FOUND THEN
                    status := true;
                    message := 'Booking available in single slot for specified day';
                ELSE
                    -- CHANGED: Consider ONLY earliest slot + its immediate next slot for the SAME resource
                    -- Find earliest slot for the day
                    SELECT db_id, resource_id, start_time::time,
                           (total_cap_in_minutes - booked_cap_in_minutes) AS avail
                      INTO found_capacity_id_, resource_id_, cur_start_time_, remaining_capacity_
                      FROM cl_tx_capacity
                     WHERE resource_id LIKE resource_pattern_
                       AND usr_tmzone_day = slot_day_
                       AND available_capacity > 0
                       AND utilization < 1
                       AND booked_cap_in_minutes < total_cap_in_minutes
                     ORDER BY start_time ASC
                     LIMIT 1;

                    IF FOUND THEN
                        SELECT db_id,
                               (total_cap_in_minutes - booked_cap_in_minutes) AS avail
                          INTO next_db_id_, next_avail_
                          FROM cl_tx_capacity
                         WHERE resource_id = resource_id_
                           AND usr_tmzone_day = slot_day_
                           AND available_capacity > 0
                           AND utilization < 1
                           AND booked_cap_in_minutes < total_cap_in_minutes
                           AND start_time::time > cur_start_time_::time
                         ORDER BY start_time ASC
                         LIMIT 1;

                        sum_avail_ := COALESCE(remaining_capacity_, 0) + COALESCE(next_avail_, 0);

                        IF sum_avail_ >= required_man_minutes_ THEN
                            status := true;
                            message := 'Booking available across two consecutive slots for the day';
                        ELSE
                         --Booking failed - Insufficient capacity across current + next slot
                            message := 'Booking failed - Insufficient capacity across earliest + next slot for the day';
                        END IF;
                    ELSE
                        message := 'Booking failed - No capacity rows found for specified day';
                    END IF;
                END IF;

            -- SCENARIO 2c: no slot_day provided, find next available
            ELSE
                -- First try to find single slot with enough capacity
                SELECT db_id, usr_tmzone_day, total_cap_in_minutes, booked_cap_in_minutes, resource_id, start_time::time
                  INTO found_capacity_id_, slot_day_, total_cap_in_minutes_, booked_cap_in_minutes_, resource_id_, cur_start_time_
                  FROM cl_tx_capacity
                 WHERE resource_id LIKE resource_pattern_
                   AND usr_tmzone_day >= (now() at time zone 'utc')::date
                   AND utilization < 1
                   AND available_capacity > 0
                   AND (total_cap_in_minutes - booked_cap_in_minutes) >= required_man_minutes_
                 ORDER BY usr_tmzone_day ASC, start_time ASC
                 LIMIT 1;

                IF FOUND THEN
                    status := true;
                    message := 'Booking available in next available single slot';
                ELSE
                    -- CHANGED: Consider ONLY the earliest slot overall + its immediate next slot (same resource)
                    -- Find earliest slot overall
                    SELECT db_id, resource_id, usr_tmzone_day, start_time::time,
                           (total_cap_in_minutes - booked_cap_in_minutes) AS avail
                      INTO found_capacity_id_, resource_id_, cur_day_, cur_start_time_, remaining_capacity_
                      FROM cl_tx_capacity
                     WHERE resource_id LIKE resource_pattern_
                       AND usr_tmzone_day >= (now() at time zone 'utc')::date
                       AND available_capacity > 0
                       AND utilization < 1
                       AND booked_cap_in_minutes < total_cap_in_minutes
                     ORDER BY usr_tmzone_day ASC, start_time ASC
                     LIMIT 1;

                    IF FOUND THEN
                        SELECT db_id,
                               (total_cap_in_minutes - booked_cap_in_minutes) AS avail
                          INTO next_db_id_, next_avail_
                          FROM cl_tx_capacity
                         WHERE resource_id = resource_id_
                           AND available_capacity > 0
                           AND utilization < 1
                           AND booked_cap_in_minutes < total_cap_in_minutes
                           AND (usr_tmzone_day, start_time::time) > (cur_day_, cur_start_time_::time)
                         ORDER BY usr_tmzone_day ASC, start_time ASC
                         LIMIT 1;

                        sum_avail_ := COALESCE(remaining_capacity_, 0) + COALESCE(next_avail_, 0);

                        IF sum_avail_ >= required_man_minutes_ THEN
                            status := true;
                            slot_day_ := cur_day_;
                            message := 'Booking available across two consecutive slots';
                        ELSE
                         --Booking failed - No capacity available across earliest + next slot
                            message := 'Booking failed - No capacity available across earliest + next slot';
                        END IF;
                    ELSE
                        message := 'Booking failed - No capacity rows found';
                    END IF;
                END IF;
            END IF;
        ELSE
            message := 'Booking failed - Pincode is not provided';
        END IF;
    END IF;
    
    -- Build response data
    IF status THEN
        resp_data := json_build_object(
            'booking_available', true,
            'required_man_minutes', required_man_minutes_,
            'capacity_id', found_capacity_id_,
            'slot_day', slot_day_,
            'message', message
        );
    ELSE
        resp_data := json_build_object(
            'booking_available', false,
            'required_man_minutes', required_man_minutes_,
            'message', message
        );
    END IF;

    RETURN json_build_object('status', status, 'message', message, 'data', resp_data);
END;
$function$
;
