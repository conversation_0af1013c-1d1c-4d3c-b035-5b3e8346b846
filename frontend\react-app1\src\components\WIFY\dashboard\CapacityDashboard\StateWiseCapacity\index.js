import React, { useState, useEffect } from 'react';
import { Table, Spin, Alert, Button, Tooltip } from 'antd';
import Widget from '../../../../Widget';
import http_utils from '../../../../../util/http_utils';
import { MdOutlineVerifiedUser } from 'react-icons/md';
import {
    CheckCircleFilled,
    CiCircleFilled,
    NumberOutlined,
    PercentageOutlined,
    UserOutlined,
    UserSwitchOutlined,
} from '@ant-design/icons';

// API endpoint
const protoUrl = '/ace-capacity-dashboard/state-wise';

const StateWiseCapacity = ({ verticalId, parentViewData }) => {
    const [isLoadingViewData, setIsLoadingViewData] = useState(false);
    const [viewData, setViewData] = useState(undefined);
    const [error, setError] = useState('');

    useEffect(() => {
        initViewData();
    }, [verticalId]);

    const initViewData = () => {
        if (isLoadingViewData) return;

        setIsLoadingViewData(true);
        setViewData(undefined);
        setError(undefined);
        let params = {
            vertical_id: verticalId,
        };
        const onComplete = (resp) => {
            setIsLoadingViewData(false);
            setViewData(resp.data);
            // Expect table_data to be an array of objects
            // Expect skills_data to be an array of objects
        };
        const onError = (error) => {
            setIsLoadingViewData(false);
            setError(http_utils.decodeErrorToMessage(error));
        };
        http_utils.performGetCall(protoUrl, params, onComplete, onError);
    };

    // Define table columns
    const getColumns = () => {
        if (!viewData) return [];
        let returnData = [
            {
                title: 'State',
                dataIndex: 'state',
                key: 'state',
                sorter: (a, b) => a.state.localeCompare(b.state),
            },
            {
                title: 'City',
                dataIndex: 'city',
                key: 'city',
                sorter: (a, b) => a.city.localeCompare(b.city),
            },
            // {
            //     title: 'Hub ID',
            //     dataIndex: 'hub_ids',
            //     key: 'hub_ids',
            //     render: (text) => text.toString(),
            // },
            // Hub ids count (its a json array)
            {
                title: 'Hubs',
                dataIndex: 'hub_ids',
                key: 'hub_ids_count',
                render: (text) => text.length,
            },
            ...viewData?.skills_data?.map((skill) => ({
                title: skill.skill_name,
                children: [
                    {
                        title: (
                            <Tooltip
                                title={`Total number of onfield users assigned to one of the hub and have ${skill.skill_name} skill`}
                            >
                                <div className="gx-text-center">
                                    <UserSwitchOutlined /> Capacity
                                </div>
                            </Tooltip>
                        ),
                        dataIndex: `skill_${skill.skill_id}_total`,
                        key: `${skill.skill_id}_total`,
                        render: (_, record) => {
                            const entry = (viewData.user_skill_data || []).find(
                                (e) =>
                                    e.state === record.state &&
                                    e.city === record.city &&
                                    e.skill_id === skill.skill_id
                            );
                            return entry?.total ?? 0;
                        }, // Replace with actual data for Total
                    },
                    {
                        title: (
                            <Tooltip
                                title={`Available users - ${skill.skill_name}`}
                            >
                                <div className="gx-text-center">
                                    <CheckCircleFilled className="gx-text-green" />{' '}
                                    Available
                                </div>
                            </Tooltip>
                        ),
                        dataIndex: `skill_${skill.skill_id}_available`,
                        key: `${skill.skill_id}_available`,
                        render: (_, record) => {
                            const entry = (viewData.user_skill_data || []).find(
                                (e) =>
                                    e.state === record.state &&
                                    e.city === record.city &&
                                    e.skill_id === skill.skill_id
                            );
                            return entry?.available ?? 0;
                        }, // Replace with actual data for Available
                    },
                    // Utilization
                    {
                        title: (
                            <Tooltip title={`ManMinutes Utilized`}>
                                <div className="gx-text-center">
                                    <NumberOutlined className="gx-text-orange" />{' '}
                                    Utilization
                                </div>
                            </Tooltip>
                        ),
                        dataIndex: `skill_${skill.skill_id}_utilization`,
                        key: `${skill.skill_id}_utilization`,
                        render: (text) => 0, // Replace with actual data for Utilization
                    },
                ],
            })),
        ];
        return returnData;
    };

    return (
        <Widget title="State-wise Capacity Dashboard" styleName="gx-card-table">
            {isLoadingViewData && (
                <div className="gx-text-center">
                    <Spin size="large" />
                    <p>Loading state-wise capacity data...</p>
                </div>
            )}
            {!isLoadingViewData && error && (
                <div>
                    <p className="gx-text-red">{error}</p>
                    <Button
                        type="secondary"
                        className=" gx-my-2"
                        onClick={() => initViewData()}
                    >
                        Retry
                    </Button>
                </div>
            )}

            {viewData && (
                <div className="gx-table-responsive">
                    <Table
                        columns={getColumns()}
                        dataSource={viewData.table_data}
                        pagination={{
                            pageSize: 10,
                            showSizeChanger: true,
                            pageSizeOptions: ['10', '20', '50', '100'],
                        }}
                        bordered
                        rowClassName={() => 'bold-row'}
                    />
                </div>
            )}
        </Widget>
    );
};

export default StateWiseCapacity;
