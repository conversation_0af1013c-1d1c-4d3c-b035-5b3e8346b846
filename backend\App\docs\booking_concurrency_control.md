# Booking Concurrency Control Implementation

## Overview

This document describes the concurrency control mechanisms implemented to prevent double-booking and ensure data consistency in the booking system.

## Problem Statement

The original `tms_create_booking_v2` function was susceptible to race conditions where multiple concurrent booking requests could:
1. Read the same available capacity
2. Both determine there's sufficient capacity
3. Both create bookings simultaneously
4. Result in overbooking of time slots

## Solution Components

### 1. Row-Level Locking in Main Function

**File**: `backend/App/dbFunctions/tms_create_booking_v2.sql`

Added `FOR UPDATE` clauses to critical capacity queries:

```sql
-- Single slot capacity check
SELECT available_capacity, total_cap_in_minutes, booked_cap_in_minutes, resource_id
FROM cl_tx_capacity
WHERE db_id = capacity_id_ AND utilization < 1
FOR UPDATE;

-- First available slot for across-slots booking
SELECT db_id, usr_tmzone_day, start_time
FROM cl_tx_capacity
WHERE resource_id = resource_id_ AND available_capacity > 0
ORDER BY usr_tmzone_day ASC, start_time ASC
LIMIT 1
FOR UPDATE;

-- Across-slots allocation loop
SELECT db_id, total_cap_in_minutes, booked_cap_in_minutes, start_time
FROM cl_tx_capacity
WHERE db_id = ANY(adjacent_slots_ids)
ORDER BY usr_tmzone_day ASC, start_time ASC
FOR UPDATE;
```

### 2. Database Triggers

**File**: `backend/App/dbFunctions/booking_concurrency_triggers.sql`

#### Trigger Functions:

1. **`trg_prevent_capacity_overbooking()`**
   - Prevents capacity updates that would result in utilization > 1.0
   - Triggered BEFORE UPDATE on `cl_tx_capacity`
   - Raises exception if overbooking would occur

2. **`trg_validate_booking_creation()`**
   - Validates booking creation against available capacity
   - Triggered BEFORE INSERT on `cl_tx_bookings`
   - Locks capacity row and checks remaining capacity

3. **`trg_auto_update_capacity_on_booking()`** (Optional)
   - Automatically updates capacity when bookings change
   - Can be enabled if automatic capacity sync is desired
   - Currently commented out to allow manual capacity management

#### Triggers Created:

```sql
-- Prevent overbooking on capacity updates
CREATE TRIGGER trg_capacity_overbooking_check
    BEFORE UPDATE ON public.cl_tx_capacity
    FOR EACH ROW
    WHEN (OLD.booked_cap_in_minutes IS DISTINCT FROM NEW.booked_cap_in_minutes)
    EXECUTE FUNCTION public.trg_prevent_capacity_overbooking();

-- Validate booking creation
CREATE TRIGGER trg_booking_validation
    BEFORE INSERT ON public.cl_tx_bookings
    FOR EACH ROW
    EXECUTE FUNCTION public.trg_validate_booking_creation();
```

### 3. Database Schema Enhancements

**File**: `backend/App/migrations/366_booking_concurrency_triggers.sql`

- Added `is_cancelled` column to `cl_tx_bookings` if not exists
- Added performance indexes for locking operations
- Added constraints to ensure data integrity

## How It Works

### Booking Process Flow

1. **Transaction Start**: Each booking request runs in a transaction
2. **Capacity Lock**: `FOR UPDATE` locks the relevant capacity rows
3. **Availability Check**: Validates sufficient capacity exists
4. **Booking Creation**: Creates booking record (triggers validation)
5. **Capacity Update**: Updates booked capacity (triggers overbooking check)
6. **Transaction Commit**: All changes committed atomically

### Concurrency Scenarios

#### Scenario 1: Two Simultaneous Bookings for Same Slot
1. Request A locks capacity row with `FOR UPDATE`
2. Request B waits for lock to be released
3. Request A completes booking and updates capacity
4. Request B gets lock, sees reduced capacity, may fail if insufficient

#### Scenario 2: Across-Slots Booking Conflicts
1. Multiple slots are locked in chronological order
2. Prevents partial bookings that could conflict
3. Ensures atomic allocation across multiple time slots

## Performance Considerations

### Indexes Added
```sql
-- For efficient locking
CREATE INDEX cl_tx_capacity_db_id_utilization_idx 
ON cl_tx_capacity (db_id, utilization) WHERE utilization < 1;

-- For booking queries
CREATE INDEX cl_tx_bookings_capacity_id_cancelled_idx 
ON cl_tx_bookings (capacity_id, is_cancelled);
```

### Lock Duration
- Locks are held only during the booking transaction
- Typical duration: 50-200ms per booking
- Locks are automatically released on transaction commit/rollback

## Error Handling

### Trigger Exceptions
```sql
-- Overbooking prevention
RAISE EXCEPTION 'Booking would exceed capacity. Current booked: % minutes, Total capacity: % minutes'
USING ERRCODE = 'check_violation';

-- Insufficient capacity
RAISE EXCEPTION 'Insufficient capacity for booking. Requested: % minutes, Available: % minutes'
USING ERRCODE = 'check_violation';
```

### Application Handling
The booking function will receive these exceptions and can:
1. Return appropriate error messages to the user
2. Suggest alternative time slots
3. Retry with different parameters

## Monitoring and Debugging

### Logging
- Trigger functions include `RAISE NOTICE` statements for debugging
- Capacity changes are logged with details
- Booking validations are logged

### Monitoring Queries
```sql
-- Check for blocked queries
SELECT * FROM pg_stat_activity WHERE wait_event_type = 'Lock';

-- Monitor trigger performance
SELECT schemaname, tablename, n_tup_ins, n_tup_upd 
FROM pg_stat_user_tables 
WHERE tablename IN ('cl_tx_capacity', 'cl_tx_bookings');
```

## Deployment Steps

1. **Apply Migration**: Run `366_booking_concurrency_triggers.sql`
2. **Update Function**: Deploy updated `tms_create_booking_v2.sql`
3. **Test**: Verify concurrent booking scenarios
4. **Monitor**: Watch for lock contention and performance impact

## Testing Recommendations

### Concurrent Booking Test
```sql
-- Simulate concurrent bookings (run in separate sessions)
BEGIN;
SELECT tms_create_booking_v2(123, '{"capacity_id": 456, ...}');
-- Hold transaction open to test locking
```

### Load Testing
- Test with multiple simultaneous booking requests
- Monitor lock wait times and deadlocks
- Verify no overbooking occurs under load

## Rollback Plan

If issues arise:
1. Drop triggers: `DROP TRIGGER trg_capacity_overbooking_check ON cl_tx_capacity;`
2. Revert function: Remove `FOR UPDATE` clauses
3. Monitor for any booking conflicts
4. Investigate and fix issues before re-enabling

## Future Enhancements

1. **Deadlock Detection**: Add retry logic for deadlock scenarios
2. **Queue System**: Implement booking queue for high-contention slots
3. **Optimistic Locking**: Consider optimistic concurrency control for better performance
4. **Monitoring Dashboard**: Real-time monitoring of booking conflicts and performance
